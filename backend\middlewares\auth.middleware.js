import jwt from 'jsonwebtoken';
import redis from '../services/redis.services.js';

export const authUser = async (req,res,next) =>{
    console.log(req.headers);
    try{
        const token = req.headers.authorization?.split(' ')[1] || req.cookies.token;

        const isBlackListed = await redis.get(token);
        if(isBlackListed){
            res.cookie('token', '');
            return res.status(401).json({
                error: 'Unauthorized'
            });
        }

        if(!token){
            return res.status(401).json({
                error: 'Unauthorized'
            });
        }

        const decoded = jwt.verify(token,process.env.JWT_SECRET);
        req.user = decoded;
        next();

    }
    catch(err){
        console.log(err);
        res.status(401).json({error: 'Unauthorized'});
    }
}