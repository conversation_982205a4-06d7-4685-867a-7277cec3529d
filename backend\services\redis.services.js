import dotenv from 'dotenv';
dotenv.config();
import Redis from 'ioredis';

// Debug environment variables
console.log('Redis Config:', {
    host: process.env.REDIS_HOST,
    port: process.env.REDIS_PORT,
    password: process.env.REDIS_PASSWORD ? '***' : 'undefined'
});

const redis = new Redis({
    host: process.env.REDIS_HOST,
    port: process.env.REDIS_PORT,
    password: process.env.REDIS_PASSWORD,
    retryDelayOnFailover: 100,
    enableReadyCheck: false,
    maxRetriesPerRequest: 3,
    lazyConnect: true
});

redis.on('connect', () => {
    console.log('Connected to Redis');
});

redis.on('error', (err) => {
    console.error('Redis connection error:', err.message);
});

redis.on('close', () => {
    console.log('Redis connection closed');
});

// Test Redis connection on startup
redis.ping().then(() => {
    console.log('Connected to Redis');
}).catch((err) => {
    console.warn('Redis not available:', err.message);
});

export default redis;