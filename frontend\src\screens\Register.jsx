import React, { useState } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { motion } from 'framer-motion';
import axiosInstance from '../config/axios';

function Register() {
  const navigate = useNavigate();
  const [form, setForm] = useState({
    email: '',
    password: '',
  });

  const handleChange = (e) => {
    setForm({ ...form, [e.target.name]: e.target.value });
  };

  const handleSubmit = (e) => {
    e.preventDefault();

    axiosInstance
      .post('/users/register', form)
      .then((response) => {
        console.log('Register successful:', response.data);

        // Store the token in localStorage
        if (response.data.token) {
          localStorage.setItem('token', response.data.token);
        }

        navigate('/');
      })
      .catch((error) => {
        console.error('Register failed:', error);
        // You might want to show an error message to the user here
      });
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-[#0f0c29] via-[#302b63] to-[#24243e] flex items-center justify-center px-4">
      <motion.div
        initial={{ opacity: 0, scale: 0.8 }}
        animate={{ opacity: 1, scale: 1 }}
        transition={{ duration: 0.6 }}
        className="w-full max-w-md bg-white/10 backdrop-blur-xl border border-white/20 p-8 rounded-3xl shadow-2xl text-white"
      >
        <motion.h2
          initial={{ y: -20, opacity: 0 }}
          animate={{ y: 0, opacity: 1 }}
          transition={{ delay: 0.2, duration: 0.6 }}
          className="text-4xl font-bold text-center mb-8 tracking-wide"
        >
          Create Account ✨
        </motion.h2>

        <motion.form
          onSubmit={handleSubmit}
          className="space-y-6"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 0.3 }}
        >
          <div className="relative">
            <label htmlFor="email" className="text-sm text-gray-300 mb-1 block">Email</label>
            <input
              type="email"
              name="email"
              placeholder="<EMAIL>"
              value={form.email}
              onChange={handleChange}
              required
              className="w-full px-4 py-2.5 rounded-xl bg-gray-800/70 text-white placeholder-gray-400 border border-gray-600 focus:ring-2 focus:ring-blue-500 focus:outline-none transition-all duration-200"
            />
          </div>

          <div className="relative">
            <label htmlFor="password" className="text-sm text-gray-300 mb-1 block">Password</label>
            <input
              type="password"
              name="password"
              placeholder="••••••••"
              value={form.password}
              onChange={handleChange}
              required
              className="w-full px-4 py-2.5 rounded-xl bg-gray-800/70 text-white placeholder-gray-400 border border-gray-600 focus:ring-2 focus:ring-purple-500 focus:outline-none transition-all duration-200"
            />
          </div>

          <motion.button
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            type="submit"
            className="w-full bg-gradient-to-r from-green-500 to-blue-500 text-white py-2.5 font-semibold rounded-xl shadow-lg hover:opacity-90 transition-all duration-300"
          >
            Register
          </motion.button>
        </motion.form>

        <motion.div
          className="mt-6 text-center text-sm text-gray-400"
          initial={{ opacity: 0, y: 10 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.5 }}
        >
          Already have an account?{' '}
          <Link to="/login" className="text-blue-400 hover:text-blue-300 font-medium">
            Sign in
          </Link>
        </motion.div>
      </motion.div>
    </div>
  );
}

export default Register;
