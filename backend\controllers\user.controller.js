import userModel from '../models/user.models.js';
import * as userService from '../services/user.services.js';
import { validationResult } from 'express-validator';
import redis from '../services/redis.services.js';

export const createUserController = async (req, res) => {

    const errors = validationResult(req);

    if (!errors.isEmpty()) {
        return res.status(400).json({
            errors: errors.array()
        })
    }

    try {
        const user = await userService.createUser(req.body);

        const token = await user.generateJWT();

        // Store token in Redis with error handling
        try {
            await redis.set(token, JSON.stringify(user), 'EX', 60*60*24);
        } catch (redisError) {
            console.warn('Redis operation failed:', redisError.message);
            // Continue without Redis caching
        }

        res.status(201).json({
            user, token
        })
    }

    catch (error) {
        res.status(500).json({
            error: error.message
        })
    }
}

export const loginController = async (req, res) => {
    const errors = validationResult(req);

    if (!errors.isEmpty()) {
        return res.status(400).json({
            errors: errors.array()
        })
    }

    try {

        const { email } = req.body;
        const user = await userModel.findOne({ email }).select('+password')

        if (!user) {
            return res.status(400).json({
                error: 'Invalid email or password'
            })
        }

        const isMatch = await user.isValidPassword(req.body.password);

        if (!isMatch) {
            return res.status(400).json({
                error: 'Invalid email or password'
            })
        }

        const token = await user.generateJWT();

        redis.set(token, JSON.stringify(user), 'EX',60*60*24);

        res.status(200).json({
            user, token
        });


    }
    catch (err) {
        res.status(500).json({
            error: err.message
        })
    }
}

export const profileController = async (req, res) => {
    console.log(req.user);

    res.status(200).json({user: req.user});
}

export const logoutController = async (req, res) => {
     
    try{

        const token = req.headers.authorization?.split(' ')[1] || req.cookies.token;
        if(!token){
            return res.status(401).json({
                error: 'Unauthorized'
            });
        }
          redis.set(token,'logout','EX',60*60*24);
          
        res.status(200).json({
            message: 'Logged out successfully'
        })
    }
    catch(err){
        res.status(500).json({
            error: err.message
        })
    }

   
}